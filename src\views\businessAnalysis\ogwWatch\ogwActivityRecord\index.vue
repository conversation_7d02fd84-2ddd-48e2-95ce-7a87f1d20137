<template>
  <div class="ogwActivityRecord">
    <div class="head-box">
      <ul>
        <li
          @click="toInfo('use')"
          :class="{ active: isActive === 'useChemical' }"
        >
          化学药剂使用
        </li>
        <li
          @click="toInfo('filter')"
          :class="{ active: isActive === 'filterChemical' }"
        >
          化学药剂筛选
        </li>
        <li
          @click="toInfo('evaluation')"
          :class="{ active: isActive === 'evaluateChemical' }"
        >
          化学药剂评价
        </li>
        <li
          @click="toInfo('test')"
          :class="{ active: isActive === 'testChemical' }"
        >
          分析化验
        </li>
      </ul>
    </div>
    <div class="content-box">
      <router-view></router-view>
    </div>
  </div>
</template>
<script>
export default {
  name: "ogwActivityRecord",
  mounted() {
    // 初始化路由状态
    this.isActive = this.$route.name;
    // 监听浏览器缩放变化
    this.handleZoomChange();
  },
  data() {
    return {
      isActive: "use",
      zoomLevel: 1,
    };
  },
  methods: {
    toInfo(name) {
      switch (name) {
        case "use":
          this.$router.push({ name: "useChemical" });
          this.isActive = "useChemical";
          break;
        case "filter":
          this.$router.push({ name: "filterChemical" });
          this.isActive = "filterChemical";
          break;
        case "evaluation":
          this.$router.push({ name: "evaluateChemical" });
          this.isActive = "evaluateChemical";
          break;
        case "test":
          this.$router.push({ name: "testChemical" });
          this.isActive = "testChemical";
          break;
      }
    },
    // 处理浏览器缩放变化
    handleZoomChange() {
      const updateZoom = () => {
        // 使用更准确的方法检测浏览器缩放级别
        let zoomLevel = 1;

        // 方法1: 使用 devicePixelRatio 和 window.outerWidth/innerWidth
        if (window.devicePixelRatio) {
          zoomLevel = window.devicePixelRatio;
        }

        // 方法2: 使用视觉视口（更准确）
        if (window.visualViewport) {
          zoomLevel = window.visualViewport.scale || 1;
        }

        // 方法3: 备用方法 - 检测屏幕宽度变化
        if (zoomLevel === 1) {
          const screenWidth = screen.width;
          const windowWidth = window.innerWidth;
          zoomLevel = Math.round((screenWidth / windowWidth) * 100) / 100;
        }

        if (Math.abs(this.zoomLevel - zoomLevel) > 0.01) {
          this.zoomLevel = zoomLevel;
          // 更新CSS变量
          this.$el.style.setProperty('--zoom-scale', zoomLevel);
        }
      };

      // 监听多种事件以确保捕获缩放变化
      const events = ['resize', 'orientationchange', 'zoom'];
      events.forEach(event => {
        window.addEventListener(event, updateZoom);
      });

      // 监听视觉视口变化（最准确的缩放检测）
      if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', updateZoom);
        window.visualViewport.addEventListener('scroll', updateZoom);
      }

      // 初始检测
      updateZoom();

      // 定期检查（作为备用方案）
      this.zoomCheckInterval = setInterval(updateZoom, 1000);
    },
  },
  beforeDestroy() {
    // 清理事件监听器
    const events = ['resize', 'orientationchange', 'zoom'];
    events.forEach(event => {
      window.removeEventListener(event, this.handleZoomChange);
    });

    if (window.visualViewport) {
      window.visualViewport.removeEventListener('resize', this.handleZoomChange);
      window.visualViewport.removeEventListener('scroll', this.handleZoomChange);
    }

    // 清理定时器
    if (this.zoomCheckInterval) {
      clearInterval(this.zoomCheckInterval);
    }
  },
};
</script>
<style lang="scss" scoped>
.ogwActivityRecord {
  width: 100%;
  height: 100%;
  padding: 20px;
  // 确保字体大小能够响应浏览器缩放
  font-size: 1rem;
  // 使用CSS变量支持动态调整
  --base-font-size: 1rem;
  --zoom-scale: 1;

  // 确保所有文本元素都能响应缩放
  * {
    font-size: inherit;
  }

  .head-box {
    width: 100%;
    height: 50px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    ul {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      transition: border-color 0.3s ease;

      li {
        cursor: pointer;
        margin-right: 40px;
        position: relative;
        padding: 0 8px;
        height: 100%;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        border-radius: 4px;
        // 使用相对单位确保文本能够响应浏览器缩放
        font-size: calc(var(--base-font-size) * var(--zoom-scale));

        &:hover {
          background: rgba(22, 119, 255, 0.05);
          color: #1677ff;
        }
      }
    }
  }

  .content-box {
    margin-top: 20px;
  }
}

// 默认激活状态样式
.active {
  color: #1677ff !important;
  font-weight: 500;
}

.active::after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  background: #1677ff;
  position: absolute;
  bottom: -1px;
  left: 0;
  transition: all 0.3s ease;
}

/* 深色主题样式 */
[data-theme="dark"] .ogwActivityRecord {
  background: #162549;

  .head-box {
    background: #1A2E52;
    color: #CCE4FF;
    border: 1px solid #4F98F6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    ul {
      border-bottom: 1px solid #4F98F6;

      li {
        color: #CCE4FF;

        &:hover {
          background: rgba(79, 152, 246, 0.1);
          color: #4EA0FC;
        }
      }
    }
  }

  .active {
    color: #4EA0FC !important;
  }

  .active::after {
    background: #4EA0FC;
  }
}

/* 浅色主题样式 */
[data-theme="tint"] .ogwActivityRecord {
  background: #E7EFF9;

  .head-box {
    background: #fff;
    color: #2E3641;
    border: 1px solid #EAEFF5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    ul {
      border-bottom: 1px solid #EAEFF5;

      li {
        color: #2E3641;

        &:hover {
          background: rgba(22, 119, 255, 0.05);
          color: #1677ff;
        }
      }
    }
  }

  .active {
    color: #1677ff !important;
  }

  .active::after {
    background: #1677ff;
  }
}
</style>
